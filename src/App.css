/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --gold: #D4AF37;
  --navy: #1A2A3A;
  --light-gold: #F4E4BC;
  --dark-gold: #B8941F;
  --beige: #FEFCF7;
  --white: #FFFFFF;
  --gray: #4B5563;
  --light-gray: #E5E7EB;
  --dark-gray: #374151;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--navy);
  background-color: var(--beige);
  font-size: 16px;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

main {
  flex: 1;
  width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--navy);
}

h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

h2 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  margin-bottom: 1rem;
}

h3 {
  font-size: clamp(1.25rem, 2.5vw, 2rem);
  margin-bottom: 0.75rem;
}

h4 {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  margin-bottom: 0.5rem;
}

p {
  margin-bottom: 1rem;
  color: var(--dark-gray);
  line-height: 1.7;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--gold);
  color: var(--white);
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background-color: var(--dark-gold);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--navy);
  color: var(--white);
  box-shadow: var(--shadow);
}

.btn-secondary:hover {
  background-color: #0F1A26;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--gold);
  border: 2px solid var(--gold);
}

.btn-outline:hover {
  background-color: var(--gold);
  color: var(--white);
}

/* Cards */
.card {
  background: var(--white);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  border: 1px solid var(--light-gold);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-gold {
  background: linear-gradient(135deg, var(--gold), var(--light-gold));
  color: var(--white);
  border: 2px solid var(--gold);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

.section {
  padding: 4rem 0;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

/* Grid */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Responsive */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .section {
    padding: 2rem 0;
  }

  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }

  .flex-row {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .section {
    padding: 1.5rem 0;
  }

  .card {
    padding: 1rem;
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}
