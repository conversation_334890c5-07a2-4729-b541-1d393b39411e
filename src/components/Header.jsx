import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, MapPin, User } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    { name: 'Reviews', href: '/reviews' },
    { name: 'Currency', href: '/currency' },
    { name: 'Itinerary', href: '/itinerary' },
    { name: 'Membership', href: '/membership' },
  ]

  const isActive = (path) => location.pathname === path

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <Link to="/" className="logo">
            <div className="logo-icon">
              <MapPin size={32} color="#C6A664" />
            </div>
            <div className="logo-text">
              <h1>NOMAD NEST</h1>
              <p>Where Every Nomad Finds a Nest</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="desktop-nav">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`nav-link ${isActive(item.href) ? 'active' : ''}`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Auth Buttons */}
          <div className="auth-buttons">
            <button className="btn btn-outline">
              <User size={18} />
              Sign In
            </button>
            <button className="btn btn-primary">Join Premium</button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="mobile-menu-btn"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="mobile-nav">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`mobile-nav-link ${isActive(item.href) ? 'active' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
            <div className="mobile-auth">
              <button className="btn btn-outline">Sign In</button>
              <button className="btn btn-primary">Join Premium</button>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .header {
          background: var(--white);
          box-shadow: var(--shadow);
          position: sticky;
          top: 0;
          z-index: 1000;
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem 0;
        }

        .logo {
          display: flex;
          align-items: center;
          gap: 1rem;
          text-decoration: none;
          color: inherit;
        }

        .logo-icon {
          background: var(--beige);
          padding: 0.5rem;
          border-radius: 50%;
          border: 2px solid var(--gold);
        }

        .logo-text h1 {
          font-size: 1.5rem;
          color: var(--navy);
          margin: 0;
        }

        .logo-text p {
          font-size: 0.875rem;
          color: var(--gray);
          margin: 0;
        }

        .desktop-nav {
          display: flex;
          gap: 2rem;
        }

        .nav-link {
          text-decoration: none;
          color: var(--navy);
          font-weight: 500;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          transition: all 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
          background-color: var(--light-gold);
          color: var(--navy);
        }

        .auth-buttons {
          display: flex;
          gap: 1rem;
          align-items: center;
        }

        .mobile-menu-btn {
          display: none;
          background: none;
          border: none;
          cursor: pointer;
          color: var(--navy);
        }

        .mobile-nav {
          display: none;
          flex-direction: column;
          gap: 1rem;
          padding: 1rem 0;
          border-top: 1px solid var(--light-gray);
        }

        .mobile-nav-link {
          text-decoration: none;
          color: var(--navy);
          font-weight: 500;
          padding: 0.75rem 1rem;
          border-radius: 0.5rem;
          transition: all 0.3s ease;
        }

        .mobile-nav-link:hover,
        .mobile-nav-link.active {
          background-color: var(--light-gold);
        }

        .mobile-auth {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-top: 1rem;
        }

        @media (max-width: 768px) {
          .desktop-nav,
          .auth-buttons {
            display: none;
          }

          .mobile-menu-btn {
            display: block;
          }

          .mobile-nav {
            display: flex;
          }

          .logo-text h1 {
            font-size: 1.25rem;
          }

          .logo-text p {
            font-size: 0.75rem;
          }
        }
      `}</style>
    </header>
  )
}

export default Header
